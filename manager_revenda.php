
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#1a1a2d">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Gerenciar Revendedores - Painel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        /* Reset e Variáveis CSS */
        :root {
            --sidebar-width: 260px;
            --sidebar-width-collapsed: 80px;
            --page-bg: #1a1a2d;
            --sidebar-bg: rgba(26, 26, 45, 0.9);
            --card-bg: rgba(44, 47, 74, 0.7);
            --text-color: #f0f0f0;
            --text-muted: #a0a0c0;
            --accent-color: #4e73df;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --transition-speed: 0.3s;
            --border-radius: 10px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Estilos Base */
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--page-bg);
            color: var(--text-color);
            background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), 
                              url('https://images.unsplash.com/photo-1579952363873-27f3bade9f55');
            background-position: center;
            background-size: cover;
            background-attachment: fixed;
            background-repeat: no-repeat;
            min-height: 100vh;
            line-height: 1.6;
        }

        /* Layout Principal */
        .page-wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* Sidebar */
        #sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: all var(--transition-speed) ease;
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-color) transparent;
        }

        #sidebar::-webkit-scrollbar {
            width: 6px;
        }

        #sidebar::-webkit-scrollbar-thumb {
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        #sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #fff;
            white-space: nowrap;
            transition: opacity var(--transition-speed);
        }

        #sidebar-content {
            flex-grow: 1;
            padding: 15px 0;
            overflow-y: auto;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 14px 20px;
            color: var(--text-muted);
            text-decoration: none;
            transition: all var(--transition-speed);
            font-weight: 500;
            border-left: 4px solid transparent;
            margin: 0 10px;
            border-radius: var(--border-radius);
            white-space: nowrap;
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.08);
            color: #fff;
            border-left-color: var(--accent-color);
        }

        .sidebar-item.active {
            background: rgba(78, 115, 223, 0.25);
            color: #fff;
            border-left-color: var(--accent-color);
            font-weight: 600;
        }

        .sidebar-item i {
            width: 24px;
            margin-right: 15px;
            font-size: 1.1rem;
            text-align: center;
            transition: margin var(--transition-speed);
        }

        .sidebar-item span {
            transition: opacity var(--transition-speed), transform var(--transition-speed);
        }

        #sidebar-footer {
            padding: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-item.logout:hover {
            border-left-color: var(--danger-color);
            background: rgba(220, 53, 69, 0.1);
        }

        /* Conteúdo Principal */
        #main-content {
            flex-grow: 1;
            margin-left: var(--sidebar-width);
            padding: 25px;
            transition: margin-left var(--transition-speed);
            min-height: 100vh;
        }

        /* Cabeçalho da Página */
        .page-header {
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .page-header h1 {
            font-size: clamp(1.8rem, 4vw, 2.2rem);
            font-weight: 700;
            color: #fff;
        }

        /* Cards de Conteúdo */
        .content-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
        }

        /* Controles Mobile */
        #menu-toggle-button {
            display: none;
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1001;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--box-shadow);
            transition: all var(--transition-speed);
        }

        #menu-toggle-button:hover {
            transform: scale(1.05);
        }

        #menu-toggle-button:active {
            transform: scale(0.95);
        }

        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 999;
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
        }

        /* Modo Sidebar Collapsed */
        body.sidebar-collapsed #sidebar {
            width: var(--sidebar-width-collapsed);
        }

        body.sidebar-collapsed #sidebar-header h2,
        body.sidebar-collapsed .sidebar-item span {
            opacity: 0;
            pointer-events: none;
            transform: translateX(-10px);
        }

        body.sidebar-collapsed .sidebar-item {
            justify-content: center;
            padding: 14px 0;
            margin: 0 10px;
        }

        body.sidebar-collapsed .sidebar-item i {
            margin-right: 0;
        }

        body.sidebar-collapsed #main-content {
            margin-left: var(--sidebar-width-collapsed);
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 220px;
            }
        }

        @media (max-width: 992px) {
            #sidebar {
                transform: translateX(-100%);
                z-index: 1000;
            }

            body.sidebar-open #sidebar {
                transform: translateX(0);
            }

            body.sidebar-open #overlay {
                display: block;
            }

            #main-content {
                margin-left: 0;
            }

            #menu-toggle-button {
                display: flex;
            }
        }

        @media (max-width: 768px) {
            #main-content {
                padding: 20px 15px;
            }

            .content-card {
                padding: 20px;
            }

            .sidebar-item {
                padding: 12px 15px;
            }
        }

        @media (max-width: 576px) {
            #main-content {
                padding: 15px 10px;
            }

            .content-card {
                padding: 15px;
            }

            .page-header h1 {
                font-size: 1.6rem;
            }

            #menu-toggle-button {
                width: 40px;
                height: 40px;
                top: 10px;
                left: 10px;
            }
        }

        /* Animações */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }

        /* Melhorias para PWA */
        @media (display-mode: standalone) {
            #main-content {
                padding-top: 15px;
            }
        }
    </style>
</head>
<body>
<div class="page-wrapper">
    <button id="menu-toggle-button" aria-label="Alternar menu">
        <i class="fas fa-bars"></i>
    </button>
    <div id="overlay" onclick="toggleSidebar()"></div>
    
    <aside id="sidebar">
        <div id="sidebar-header">
            <h2>FutBanner</h2>
        </div>
        
        <nav id="sidebar-content" aria-label="Menu principal">
            <a href="index.php" class="sidebar-item" aria-current="page">
                <i class="fa-solid fa-house"></i>
                <span>Dashboard</span>
            </a>
            <a href="futbanner.php" class="sidebar-item">
                <i class="fas fa-futbol"></i>
                <span>Banner Fut</span>
            </a>
            <a href="painel.php" class="sidebar-item">
            <i class="fas fa-film"></i>
            <span>Banner Filmes</span>
        </a>
            <a href="logo.php" class="sidebar-item">
                <i class="fas fa-image"></i>
                <span>Logo</span>
            </a>
            <a href="background.php" class="sidebar-item">
                <i class="fas fa-photo-video"></i>
                <span>Fundo</span>
            </a>
            
                            <a href="card.php" class="sidebar-item">
                    <i class="fas fa-th-large"></i>
                    <span>Card Jogos</span>
                </a>
                <a href="aprov_redefinirsenha.php" class="sidebar-item">  <!-- Corrigido o href e ícone -->
                    <i class="fa-solid fa-fingerprint"></i>
                    <span>Aprov-Redefinir Pass</span>
                </a>
                
                <a href="config_cronbot.php" class="sidebar-item">
                    <i class="fa-solid fa-robot"></i>
                    <span>Config Bot</span>
                </a>
                        
                            <a href="manager_revenda.php" class="sidebar-item">
                    <i class="fas fa-users-cog"></i>
                    <span>Revendedores</span>
                </a>
                <a href="setting.php" class="sidebar-item">
                    <i class="fas fa-cog"></i>
                    <span>Credenciais</span>
                </a>
                    </nav>
        
        <div id="sidebar-footer">
            <a href="logout.php" class="sidebar-item logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </aside>
    
    <main id="main-content">
<style>
    :root {
        --primary-color: #4361ee;
        --secondary-color: #3f37c9;
        --success-color: #4cc9f0;
        --danger-color: #f72585;
        --warning-color: #f8961e;
        --info-color: #4895ef;
        --dark-color: #212529;
        --light-color: #f8f9fa;
        --gray-color: #6c757d;
        --border-radius: 0.375rem;
        --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --transition: all 0.3s ease;
    }

    /* Layout Base */
    body {
        font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #f5f7fa;
        color: #333;
        line-height: 1.6;
    }

    .content-card {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .page-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    /* Alertas */
    .alert {
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius);
        border: 1px solid transparent;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    /* Tabela */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        background-color: white;
    }

    .table th,
    .table td {
        padding: 1rem;
        text-align: left;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }

    .table tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table tbody tr:nth-of-type(even) {
        background-color: rgba(0, 0, 0, 0.01);
    }

    /* Badges */
    .badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 600;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 50rem;
    }

    .badge-admin {
        color: white;
        background-color: var(--danger-color);
    }

    .badge-revenda {
        color: white;
        background-color: var(--secondary-color);
    }

    .badge-subrevenda {
        color: white;
        background-color: var(--info-color);
    }

    .badge-success {
        color: white;
        background-color: #28a745;
    }

    .badge-warning {
        color: #212529;
        background-color: #ffc107;
    }

    /* Botões */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: var(--transition);
        cursor: pointer;
        gap: 0.5rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .btn-primary {
        color: white;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

    .btn-danger {
        color: white;
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-danger:hover {
        background-color: #d1144a;
        border-color: #c51243;
    }

    .btn-group {
        display: flex;
        gap: 0.5rem;
    }

    /* Formulários */
    .form-group {
        margin-bottom: 1.25rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .form-control {
        display: block;
        width: 100%;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
        color: #495057;
        background-color: white;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: var(--border-radius);
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
        color: #495057;
        background-color: white;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-check-input {
        width: 1em;
        height: 1em;
        margin-top: 0;
    }

    /* Modal */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1050;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }

    .modal.show {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        position: relative;
        width: 100%;
        max-width: 500px;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 1.5rem;
        margin: 0.5rem;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.25rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .modal-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        line-height: 1;
        color: #6c757d;
        cursor: pointer;
        padding: 0;
    }

    .modal-close:hover {
        color: #495057;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }

    /* Ícones */
    .icon {
        width: 1em;
        height: 1em;
        vertical-align: middle;
        fill: currentColor;
    }

    /* Utilitários */
    .text-center {
        text-align: center;
    }

    .text-right {
        text-align: right;
    }

    .mb-3 {
        margin-bottom: 1rem;
    }

    .mt-3 {
        margin-top: 1rem;
    }

    /* Responsividade */
    @media (max-width: 992px) {
        .content-card {
            padding: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .content-card {
            padding: 1rem;
            margin: 1rem;
        }

        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .table th,
        .table td {
            padding: 0.75rem;
        }

        .btn-group {
            flex-direction: column;
            gap: 0.25rem;
        }

        .btn {
            width: 100%;
        }
    }

    @media (max-width: 576px) {
        .modal-content {
            padding: 1rem;
        }

        .table th,
        .table td {
            padding: 0.5rem;
            font-size: 0.85rem;
        }

        .badge {
            font-size: 0.65rem;
            padding: 0.25em 0.5em;
        }
    }

     /* Animações */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    /* Dark Mode (opcional) */
    @media (prefers-color-scheme: dark) {
        body {
            background-color: #1a1a1a;
            color: #f0f0f0;
        }

        .content-card,
        .table,
        .modal-content {
            background-color: #2d2d2d;
            color: #f0f0f0;
        }

        .table th,
        .table td {
            border-color: #444;
        }

        .table thead th {
            background-color: #333;
            color: #f0f0f0;
            border-color: #444;
        }

        .table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .form-control {
            background-color: #333;
            border-color: #555;
            color: #f0f0f0;
        }

        .form-control:focus {
            background-color: #333;
            color: #f0f0f0;
            border-color: #4361ee;
        }

        .alert-danger {
            color: #f8d7da;
            background-color: #842029;
            border-color: #842029;
        }

        .alert-success {
            color: #d4edda;
            background-color: #0f5132;
            border-color: #0f5132;
        }
    }
</style>

<div class="content-card fade-in">
    <div class="page-header">
        <h1 class="page-title">
            <svg class="icon" viewBox="0 0 16 16" width="16" height="16">
                <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/>
            </svg>
            Gerenciar Revendedores
        </h1>
        
                    <button class="btn btn-primary" onclick="abrirModalCadastro()">
                <svg class="icon" viewBox="0 0 16 16" width="16" height="16">
                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                </svg>
                Novo Revendedor
            </button>
            </div>
    
        
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Usuário</th>
                    <th>E-mail</th>
                    <th>Tipo</th>
                    <th>Status</th>
                    <th>Data Cadastro</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                                                            <tr>
                            <td>7</td>
                            <td>admin1</td>
                            <td><EMAIL></td>
                            <td>
                                                                    <span class="badge badge-subrevenda">Sub-Revenda</span>
                                                            </td>
                            <td>
                                <span class="badge badge-success">Ativo</span>                            </td>
                            <td>19/08/2025 00:23</td>
                            <td>
                                                                    <div class="btn-group">
                                        <button class="btn btn-primary btn-sm" 
                                                onclick="abrirModalEdicao(
                                                    7,
                                                    'admin1',
                                                    '<EMAIL>',
                                                    3,
                                                    1                                                )">
                                            <svg class="icon" viewBox="0 0 16 16" width="12" height="12">
                                                <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                            </svg>
                                            Editar
                                        </button>
                                        <a href="manager_revenda.php?acao=deletar&id=7" 
                                           class="btn btn-danger btn-sm"
                                           onclick="return confirmarExclusao(event)">
                                            <svg class="icon" viewBox="0 0 16 16" width="12" height="12">
                                                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                            </svg>
                                            Excluir
                                        </a>
                                    </div>
                                                            </td>
                        </tr>
                                                </tbody>
        </table>
    </div>
</div>

<!-- Modal de Cadastro -->
<div id="modalCadastro" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">
                <svg class="icon" viewBox="0 0 16 16" width="16" height="16">
                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                </svg>
                Cadastrar Novo Revendedor
            </h2>
            <button type="button" class="modal-close" onclick="fecharModalCadastro()">&times;</button>
        </div>
        
        <form method="POST" action="manager_revenda.php" onsubmit="return validarFormCadastro()">
            <div class="form-group">
                <label for="username" class="form-label">Nome de Usuário</label>
                <input type="text" id="username" name="username" class="form-control" required minlength="4" maxlength="30">
                <small class="text-muted">Entre 4 e 30 caracteres</small>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">E-mail</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Senha</label>
                <input type="password" id="password" name="password" class="form-control" required minlength="8">
                <small class="text-muted">Mínimo 8 caracteres</small>
            </div>
            
            <div class="form-group">
                <label for="nivel" class="form-label">Tipo de Revendedor</label>
                <select id="nivel" name="nivel" class="form-control" required>
                                            <option value="2">Revendedor (Level 2)</option>
                        <option value="3">Sub-Revendedor (Level 3)</option>
                                    </select>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" onclick="fecharModalCadastro()">Cancelar</button>
                <button type="submit" name="cadastrar" class="btn btn-primary">Cadastrar</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal de Edição -->
<div id="modalEdicao" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">
                <svg class="icon" viewBox="0 0 16 16" width="16" height="16">
                    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                </svg>
                Editar Revendedor
            </h2>
            <button type="button" class="modal-close" onclick="fecharModalEdicao()">&times;</button>
        </div>
        
        <form method="POST" action="manager_revenda.php" onsubmit="return validarFormEdicao()">
            <input type="hidden" id="edit_id" name="id">
            
            <div class="form-group">
                <label for="edit_username" class="form-label">Nome de Usuário</label>
                <input type="text" id="edit_username" name="username" class="form-control" required minlength="4" maxlength="30">
            </div>
            
            <div class="form-group">
                <label for="edit_email" class="form-label">E-mail</label>
                <input type="email" id="edit_email" name="email" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="edit_nivel" class="form-label">Tipo de Revendedor</label>
                <select id="edit_nivel" name="nivel" class="form-control" required>
                                            <option value="2">Revendedor (Level 2)</option>
                        <option value="3">Sub-Revendedor (Level 3)</option>
                                    </select>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" id="edit_ativo" name="ativo" value="1" class="form-check-input">
                    <label for="edit_ativo" class="form-check-label">Ativo</label>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" onclick="fecharModalEdicao()">Cancelar</button>
                <button type="submit" name="editar" class="btn btn-primary">Salvar Alterações</button>
            </div>
        </form>
    </div>
</div>

<script>
// Funções para controle dos modais
function abrirModalCadastro() {
    document.getElementById('modalCadastro').classList.add('show');
    document.body.style.overflow = 'hidden';
}

function fecharModalCadastro() {
    document.getElementById('modalCadastro').classList.remove('show');
    document.body.style.overflow = '';
}

function abrirModalEdicao(id, username, email, nivel, ativo) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_email').value = email;
    document.getElementById('edit_nivel').value = nivel;
    document.getElementById('edit_ativo').checked = ativo == 1;
    
    document.getElementById('modalEdicao').classList.add('show');
    document.body.style.overflow = 'hidden';
}

function fecharModalEdicao() {
    document.getElementById('modalEdicao').classList.remove('show');
    document.body.style.overflow = '';
}

// Fechar modal ao clicar fora
window.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        fecharModalCadastro();
        fecharModalEdicao();
    }
});

// Validação de formulários
function validarFormCadastro() {
    const username = document.getElementById('username').value.trim();
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    
    if (username.length < 4 || username.length > 30) {
        alert('O nome de usuário deve ter entre 4 e 30 caracteres!');
        return false;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        alert('Por favor, insira um e-mail válido!');
        return false;
    }
    
    if (password.length < 8) {
        alert('A senha deve ter no mínimo 8 caracteres!');
        return false;
    }
    
    return true;
}

function validarFormEdicao() {
    const username = document.getElementById('edit_username').value.trim();
    const email = document.getElementById('edit_email').value.trim();
    
    if (username.length < 4 || username.length > 30) {
        alert('O nome de usuário deve ter entre 4 e 30 caracteres!');
        return false;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        alert('Por favor, insira um e-mail válido!');
        return false;
    }
    
    return true;
}

// Confirmação de exclusão
function confirmarExclusao(event) {
    if (!confirm('Tem certeza que deseja excluir este revendedor?\nEsta ação não pode ser desfeita!')) {
        event.preventDefault();
        return false;
    }
    return true;
}

// Fechar modais com ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        fecharModalCadastro();
        fecharModalEdicao();
    }
});
</script>

</div> <div id="overlay"></div>
    
</div> <script>
    document.addEventListener('DOMContentLoaded', () => {
        const menuToggleButton = document.getElementById('menu-toggle-button');
        const overlay = document.getElementById('overlay');
        const body = document.body;

        function toggleMenu() {
            body.classList.toggle('sidebar-open');
        }

        if(menuToggleButton) {
            menuToggleButton.addEventListener('click', toggleMenu);
        }
        if(overlay) {
            overlay.addEventListener('click', toggleMenu);
        }
        
        const currentPage = window.location.pathname.split('/').pop();
        const menuItems = document.querySelectorAll('#sidebar-content .sidebar-item');
        
        let hasActive = false;
        menuItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href') === currentPage) {
                item.classList.add('active');
                hasActive = true;
            }
        });

        if (!hasActive && (currentPage === '' || currentPage === 'index.php')) {
            const dashboardItem = document.querySelector('#sidebar-content a[href="index.php"]');
            if(dashboardItem) {
                dashboardItem.classList.add('active');
            }
        }
    });

    // --- NOVA CORREÇÃO ADICIONADA AQUI ---
    // Força o fechamento de modais do SweetAlert ao usar o botão "Voltar" do navegador.
    window.addEventListener('pageshow', function (event) {
        // A propriedade 'persisted' é true se a página foi restaurada do bfcache.
        if (event.persisted) {
            // Se a biblioteca SweetAlert2 existir e um modal estiver visível, feche-o.
            if (typeof Swal !== 'undefined' && Swal.isVisible()) {
                Swal.close();
            }
        }
    });
</script>

</body>
</html>