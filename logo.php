
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#1a1a2d">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Gerenciar Logos - Painel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        /* Reset e Variáveis CSS */
        :root {
            --sidebar-width: 260px;
            --sidebar-width-collapsed: 80px;
            --page-bg: #1a1a2d;
            --sidebar-bg: rgba(26, 26, 45, 0.9);
            --card-bg: rgba(44, 47, 74, 0.7);
            --text-color: #f0f0f0;
            --text-muted: #a0a0c0;
            --accent-color: #4e73df;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --transition-speed: 0.3s;
            --border-radius: 10px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Estilos Base */
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--page-bg);
            color: var(--text-color);
            background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), 
                              url('https://images.unsplash.com/photo-1579952363873-27f3bade9f55');
            background-position: center;
            background-size: cover;
            background-attachment: fixed;
            background-repeat: no-repeat;
            min-height: 100vh;
            line-height: 1.6;
        }

        /* Layout Principal */
        .page-wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* Sidebar */
        #sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: all var(--transition-speed) ease;
            display: flex;
            flex-direction: column;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-color) transparent;
        }

        #sidebar::-webkit-scrollbar {
            width: 6px;
        }

        #sidebar::-webkit-scrollbar-thumb {
            background-color: var(--accent-color);
            border-radius: 3px;
        }

        #sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #fff;
            white-space: nowrap;
            transition: opacity var(--transition-speed);
        }

        #sidebar-content {
            flex-grow: 1;
            padding: 15px 0;
            overflow-y: auto;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 14px 20px;
            color: var(--text-muted);
            text-decoration: none;
            transition: all var(--transition-speed);
            font-weight: 500;
            border-left: 4px solid transparent;
            margin: 0 10px;
            border-radius: var(--border-radius);
            white-space: nowrap;
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.08);
            color: #fff;
            border-left-color: var(--accent-color);
        }

        .sidebar-item.active {
            background: rgba(78, 115, 223, 0.25);
            color: #fff;
            border-left-color: var(--accent-color);
            font-weight: 600;
        }

        .sidebar-item i {
            width: 24px;
            margin-right: 15px;
            font-size: 1.1rem;
            text-align: center;
            transition: margin var(--transition-speed);
        }

        .sidebar-item span {
            transition: opacity var(--transition-speed), transform var(--transition-speed);
        }

        #sidebar-footer {
            padding: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-item.logout:hover {
            border-left-color: var(--danger-color);
            background: rgba(220, 53, 69, 0.1);
        }

        /* Conteúdo Principal */
        #main-content {
            flex-grow: 1;
            margin-left: var(--sidebar-width);
            padding: 25px;
            transition: margin-left var(--transition-speed);
            min-height: 100vh;
        }

        /* Cabeçalho da Página */
        .page-header {
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .page-header h1 {
            font-size: clamp(1.8rem, 4vw, 2.2rem);
            font-weight: 700;
            color: #fff;
        }

        /* Cards de Conteúdo */
        .content-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
        }

        /* Controles Mobile */
        #menu-toggle-button {
            display: none;
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1001;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--box-shadow);
            transition: all var(--transition-speed);
        }

        #menu-toggle-button:hover {
            transform: scale(1.05);
        }

        #menu-toggle-button:active {
            transform: scale(0.95);
        }

        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 999;
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
        }

        /* Modo Sidebar Collapsed */
        body.sidebar-collapsed #sidebar {
            width: var(--sidebar-width-collapsed);
        }

        body.sidebar-collapsed #sidebar-header h2,
        body.sidebar-collapsed .sidebar-item span {
            opacity: 0;
            pointer-events: none;
            transform: translateX(-10px);
        }

        body.sidebar-collapsed .sidebar-item {
            justify-content: center;
            padding: 14px 0;
            margin: 0 10px;
        }

        body.sidebar-collapsed .sidebar-item i {
            margin-right: 0;
        }

        body.sidebar-collapsed #main-content {
            margin-left: var(--sidebar-width-collapsed);
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 220px;
            }
        }

        @media (max-width: 992px) {
            #sidebar {
                transform: translateX(-100%);
                z-index: 1000;
            }

            body.sidebar-open #sidebar {
                transform: translateX(0);
            }

            body.sidebar-open #overlay {
                display: block;
            }

            #main-content {
                margin-left: 0;
            }

            #menu-toggle-button {
                display: flex;
            }
        }

        @media (max-width: 768px) {
            #main-content {
                padding: 20px 15px;
            }

            .content-card {
                padding: 20px;
            }

            .sidebar-item {
                padding: 12px 15px;
            }
        }

        @media (max-width: 576px) {
            #main-content {
                padding: 15px 10px;
            }

            .content-card {
                padding: 15px;
            }

            .page-header h1 {
                font-size: 1.6rem;
            }

            #menu-toggle-button {
                width: 40px;
                height: 40px;
                top: 10px;
                left: 10px;
            }
        }

        /* Animações */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }

        /* Melhorias para PWA */
        @media (display-mode: standalone) {
            #main-content {
                padding-top: 15px;
            }
        }
    </style>
</head>
<body>
<div class="page-wrapper">
    <button id="menu-toggle-button" aria-label="Alternar menu">
        <i class="fas fa-bars"></i>
    </button>
    <div id="overlay" onclick="toggleSidebar()"></div>
    
    <aside id="sidebar">
        <div id="sidebar-header">
            <h2>FutBanner</h2>
        </div>
        
        <nav id="sidebar-content" aria-label="Menu principal">
            <a href="index.php" class="sidebar-item" aria-current="page">
                <i class="fa-solid fa-house"></i>
                <span>Dashboard</span>
            </a>
            <a href="futbanner.php" class="sidebar-item">
                <i class="fas fa-futbol"></i>
                <span>Banner Fut</span>
            </a>
            <a href="painel.php" class="sidebar-item">
            <i class="fas fa-film"></i>
            <span>Banner Filmes</span>
        </a>
            <a href="logo.php" class="sidebar-item">
                <i class="fas fa-image"></i>
                <span>Logo</span>
            </a>
            <a href="background.php" class="sidebar-item">
                <i class="fas fa-photo-video"></i>
                <span>Fundo</span>
            </a>
            
                            <a href="card.php" class="sidebar-item">
                    <i class="fas fa-th-large"></i>
                    <span>Card Jogos</span>
                </a>
                <a href="aprov_redefinirsenha.php" class="sidebar-item">  <!-- Corrigido o href e ícone -->
                    <i class="fa-solid fa-fingerprint"></i>
                    <span>Aprov-Redefinir Pass</span>
                </a>
                
                <a href="config_cronbot.php" class="sidebar-item">
                    <i class="fa-solid fa-robot"></i>
                    <span>Config Bot</span>
                </a>
                        
                            <a href="manager_revenda.php" class="sidebar-item">
                    <i class="fas fa-users-cog"></i>
                    <span>Revendedores</span>
                </a>
                <a href="setting.php" class="sidebar-item">
                    <i class="fas fa-cog"></i>
                    <span>Credenciais</span>
                </a>
                    </nav>
        
        <div id="sidebar-footer">
            <a href="logout.php" class="sidebar-item logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </aside>
    
    <main id="main-content">
<style>
    .two-column-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 30px;
    }
    @media (min-width: 768px) {
        .two-column-grid {
            grid-template-columns: 1fr 1fr;
        }
    }
    .column-box {
        background: rgba(0,0,0,0.15);
        padding: 25px;
        border-radius: 10px;
    }
    .column-box h3 {
        color: var(--accent-color);
        margin-bottom: 20px;
        font-weight: 600;
        border-bottom: 1px solid var(--accent-color);
        padding-bottom: 10px;
    }
    .form-group { margin-bottom: 20px; }
    .form-group label { display: block; color: var(--text-secondary); margin-bottom: 8px; font-weight: 500; }
    .form-select, .form-control {
        width: 100%; padding: 12px 15px; background-color: var(--page-bg);
        border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: var(--text-color);
    }
    .form-select:focus, .form-control:focus { outline: none; border-color: var(--accent-color); }
    
    .preview-area {
        width: 100%;
        height: 200px;
        margin-top: 15px;
        background-color: var(--page-bg);
        background-image: 
            linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
            linear-gradient(-45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.05) 75%),
            linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.05) 75%);
        background-size: 20px 20px;
        border: 2px dashed rgba(255,255,255,0.2);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        padding: 10px;
    }
    .preview-area img { 
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    .preview-area .no-image {
        color: var(--text-muted);
        font-size: 0.9rem;
    }
    .current-method-info { 
        text-align: center; 
        color: var(--text-muted); 
        font-size: 0.9rem; 
        margin-top: 15px; 
        word-break: break-all;
    }
    .current-method-info strong { color: var(--accent-color); }
    
    .method-switcher { display: flex; border-radius: 8px; overflow: hidden; }
    .method-switcher input[type="radio"] { display: none; }
    .method-switcher label {
        flex: 1; text-align: center; padding: 12px; cursor: pointer;
        background-color: rgba(0,0,0,0.2); color: var(--text-muted);
        transition: all 0.3s; font-weight: 500;
    }
    .method-switcher input[type="radio"]:checked + label { background-color: var(--accent-color); color: #fff; }
    
    .submit-btn, .secondary-btn {
        width: 100%; padding: 12px; font-size: 1rem; font-weight: 600; color: #fff;
        border: none; border-radius: 8px; cursor: pointer; transition: all 0.3s;
        display: flex; align-items: center; justify-content: center;
    }
    .submit-btn i, .secondary-btn i { margin-right: 8px; }

    .submit-btn { background-color: var(--success-color); }
    .submit-btn:hover { background-color: #218838; transform: translateY(-2px); }

    .secondary-btn { background-color: #6c757d; }
    .secondary-btn:hover { background-color: #5a6268; }

    .divider-or { 
        text-align: center; 
        color: var(--text-muted); 
        margin: 20px 0; 
        position: relative;
    }
    .divider-or::before, .divider-or::after {
        content: "";
        flex: 1;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        margin: auto;
    }
    .divider-or::before {
        margin-right: 10px;
    }
    .divider-or::after {
        margin-left: 10px;
    }
</style>

<div class="page-header">
    <h1><i class="fas fa-image" style="color: var(--accent-color);"></i> Gerenciar Logos</h1>
</div>

<div class="content-card">
    <div class="two-column-grid">
        <div class="column-box">
            <h3>1. Selecione e Visualize</h3>
            <div class="form-group">
                <label for="logo-selector">Logo para Editar:</label>
                <select id="logo-selector" class="form-select">
                                            <option value="logo_banner_1" selected>
                            Logo Banner 1                        </option>
                                            <option value="logo_banner_2" >
                            Logo Banner 2                        </option>
                                            <option value="logo_banner_3" >
                            Logo Banner 3                        </option>
                                            <option value="logo_banner_4" >
                            Logo Banner 4                        </option>
                                            <option value="logo_banner_5" >
                            Logo Banner 5                        </option>
                                    </select>
            </div>
            
            <label>Prévia Atual:</label>
            <div class="preview-area">
                                    <img src="./fzstore/logo/user_1/logo_banner_1_1.jpg?v=1755605420" alt="Preview do Logo" onerror="this.onerror=null;this.src='imgelementos/semlogo.png';">
                            </div>
            <p class="current-method-info">Método Atual: <strong>Arquivo Enviado</strong></p>
                            <p class="current-method-info">Origem: <strong>./fzstore/logo/user_1/logo_banner_1_1.jpg</strong></p>
                    </div>

        <div class="column-box">
            <h3>2. Altere a Imagem</h3>
            <div class="method-switcher">
                <input type="radio" id="upload-radio" name="upload-type" value="file" checked>
                <label for="upload-radio"><i class="fas fa-upload"></i> Enviar Arquivo</label>
                
                <input type="radio" id="url-radio" name="upload-type" value="url">
                <label for="url-radio"><i class="fas fa-link"></i> Usar URL</label>
            </div>
            <div class="forms-container" style="margin-top: 20px;">
                <form method="post" enctype="multipart/form-data" id="upload-form" class="method-form" action="logo.php?tipo=logo_banner_1">
                    <input type="hidden" name="logo_type" value="logo_banner_1">
                    <div class="form-group">
                        <label for="image">Selecione uma imagem (PNG, JPG, GIF, WebP, SVG):</label>
                        <input class="form-control" type="file" name="image" id="image" accept="image/*">
                    </div>
                    <button class="submit-btn" type="submit" name="upload"><i class="fas fa-paper-plane"></i> Enviar</button>
                </form>

                <form method="post" id="url-form" class="method-form" style="display: none;" action="logo.php?tipo=logo_banner_1">
                    <input type="hidden" name="logo_type" value="logo_banner_1">
                    <div class="form-group">
                        <label for="image-url">Insira a URL da imagem:</label>
                        <input class="form-control" type="text" name="image-url" id="image-url" placeholder="https://...">
                    </div>
                    <button class="submit-btn" type="submit" name="url-submit"><i class="fas fa-save"></i> Salvar URL</button>
                </form>
            </div>

            <div class="divider-or">OU</div>

            <form method="post" id="default-form" action="logo.php?tipo=logo_banner_1">
                <input type="hidden" name="logo_type" value="logo_banner_1">
                <button class="secondary-btn" type="submit" name="default-logo"><i class="fas fa-undo"></i> Restaurar Padrão</button>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const logoSelector = document.getElementById('logo-selector');
    logoSelector.addEventListener('change', function() {
        window.location.href = 'logo.php?tipo=' + this.value;
    });

    const uploadRadio = document.getElementById('upload-radio');
    const urlRadio = document.getElementById('url-radio');
    const uploadForm = document.getElementById('upload-form');
    const urlForm = document.getElementById('url-form');
    
    function switchForms() {
        if (uploadRadio.checked) {
            uploadForm.style.display = 'block';
            urlForm.style.display = 'none';
        } else {
            uploadForm.style.display = 'none';
            urlForm.style.display = 'block';
        }
    }
    
    uploadRadio.addEventListener('change', switchForms);
    urlRadio.addEventListener('change', switchForms);
    switchForms();

    });
</script>

</div> <div id="overlay"></div>
    
</div> <script>
    document.addEventListener('DOMContentLoaded', () => {
        const menuToggleButton = document.getElementById('menu-toggle-button');
        const overlay = document.getElementById('overlay');
        const body = document.body;

        function toggleMenu() {
            body.classList.toggle('sidebar-open');
        }

        if(menuToggleButton) {
            menuToggleButton.addEventListener('click', toggleMenu);
        }
        if(overlay) {
            overlay.addEventListener('click', toggleMenu);
        }
        
        const currentPage = window.location.pathname.split('/').pop();
        const menuItems = document.querySelectorAll('#sidebar-content .sidebar-item');
        
        let hasActive = false;
        menuItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('href') === currentPage) {
                item.classList.add('active');
                hasActive = true;
            }
        });

        if (!hasActive && (currentPage === '' || currentPage === 'index.php')) {
            const dashboardItem = document.querySelector('#sidebar-content a[href="index.php"]');
            if(dashboardItem) {
                dashboardItem.classList.add('active');
            }
        }
    });

    // --- NOVA CORREÇÃO ADICIONADA AQUI ---
    // Força o fechamento de modais do SweetAlert ao usar o botão "Voltar" do navegador.
    window.addEventListener('pageshow', function (event) {
        // A propriedade 'persisted' é true se a página foi restaurada do bfcache.
        if (event.persisted) {
            // Se a biblioteca SweetAlert2 existir e um modal estiver visível, feche-o.
            if (typeof Swal !== 'undefined' && Swal.isVisible()) {
                Swal.close();
            }
        }
    });
</script>

</body>
</html>